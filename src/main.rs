mod common;
mod database;
mod helius;
mod raydium;
mod strategy;
use crate::common::config::load_config;
use crate::helius::price::get_sol_price_in_usd;
use crate::strategy::raydium::find_triangular_arbitrage;
use serde_json::Value;
use solana_client::nonblocking::rpc_client::RpcClient;
use solana_sdk::signature::Signer;
use solana_sdk::signature::read_keypair_file;
use solana_sdk::{
    commitment_config::CommitmentConfig, native_token::LAMPORTS_PER_SOL, pubkey::Pubkey,
};
use std::error::Error;
use std::fs;
use tracing::{error, info};

fn get_local_keypair_pubkeys() -> Vec<Pubkey> {
    let mut pubkeys = Vec::new();
    let home = std::env::var("HOME").unwrap();
    let solana_dir = format!("{}/.config/solana", home);
    let entries = fs::read_dir(&solana_dir).unwrap();

    for entry in entries {
        let entry = entry.unwrap();
        let path = entry.path();
        if path.extension().map(|s| s == "json").unwrap_or(false) {
            if let Ok(keypair) = read_keypair_file(&path) {
                pubkeys.push(keypair.pubkey());
            }
        }
    }
    pubkeys
}

#[allow(unused)]
async fn process_jupiter_msg(data: Value) -> Result<(), Box<dyn Error>> {
    if let Some(method) = data.get("method").and_then(|m| m.as_str()) {
        info!("method is {}", method);
        if method == "accountNotification" {
            if let Some(params) = data.get("params") {
                let result = &params["result"];
                let value = &result["value"];

                let lamports = value["lamports"].as_u64().unwrap_or(0);
                let owner = value["owner"].as_str().unwrap_or_default();

                info!("账户余额: {}", lamports);
                info!("账户拥有者: {}", owner);
            }
        }
    }

    Ok(())
}

#[tokio::main(flavor = "multi_thread")]
async fn main() -> Result<(), Box<dyn std::error::Error>> {
    // Initialize tracing
    tracing_subscriber::fmt::init();

    let config_path = ".config.toml";
    let config = load_config(config_path)?;
    info!("config is {:#?}", config);

    // Initialize database
    let db_path = config.db_path.clone();
    if let Err(e) = database::common::init_db_with_cfs(&db_path, &[strategy::raydium::CF_NAME]) {
        error!("Failed to initialize database: {}", e);
        return Err(e.into());
    } else {
        info!("Database initialized at: {}", db_path);
    }
    // let client =
    //     RpcClient::new_with_commitment(config.get_rpc_url(), CommitmentConfig::confirmed());

    // let price = get_sol_price_in_usd().await?;
    // let pubkeys = get_local_keypair_pubkeys();
    // for pubkey in pubkeys {
    //     match client.get_account(&pubkey).await {
    //         Ok(account) => {
    //             info!("Account for {}: {:#?}", pubkey, account);

    //             let sol_balance = account.lamports as f64 / LAMPORTS_PER_SOL as f64;
    //             info!("Balance: {} SOL", sol_balance);

    //             let usd_value = sol_balance * price;
    //             info!("Value: ${:.2}", usd_value);
    //         }
    //         Err(e) => {
    //             info!("Get account failed: {}", e);
    //         }
    //     }
    // }

    // info!("version is {:?}", raydium::common::version().await?);

    // find_triangular_arbitrage().await;
    strategy::raydium::run_triangular_arbitrage_task().await;
    // strategy::raydium::update_database().await?;
    Ok(())
}
